package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/workarea/model"
	"github.com/jackc/pgx/v5/pgxpool"
)

type WorkAreaPostgreRepo interface {
	Create(ctx context.Context, workArea model.WorkArea) error
	Update(ctx context.Context, workArea model.WorkArea) error
	GetByProp(ctx context.Context, prop string, value string) (*model.WorkArea, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]model.WorkArea, error)
	Delete(ctx context.Context, id string) error
}

type workAreaPostgreRepo struct {
	pool *pgxpool.Pool
}

func NewWorkAreaPostgreRepo(pool *pgxpool.Pool) WorkAreaPostgreRepo {
	return &workAreaPostgreRepo{
		pool: pool,
	}
}

// Create implements WorkAreaPostgreRepo.
func (w *workAreaPostgreRepo) Create(ctx context.Context, workArea model.WorkArea) error {
	// Implementation will be added
	return nil
}

// Update implements WorkAreaPostgreRepo.
func (w *workAreaPostgreRepo) Update(ctx context.Context, workArea model.WorkArea) error {
	// Implementation will be added
	return nil
}

// GetByProp implements WorkAreaPostgreRepo.
func (w *workAreaPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.WorkArea, error) {
	// Implementation will be added
	return nil, nil
}

// CountByProp implements WorkAreaPostgreRepo.
func (w *workAreaPostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	// Implementation will be added
	return 0, nil
}

// GetAll implements WorkAreaPostgreRepo.
func (w *workAreaPostgreRepo) GetAll(ctx context.Context) ([]model.WorkArea, error) {
	// Implementation will be added
	return nil, nil
}

// Delete implements WorkAreaPostgreRepo.
func (w *workAreaPostgreRepo) Delete(ctx context.Context, id string) error {
	// Implementation will be added
	return nil
}
